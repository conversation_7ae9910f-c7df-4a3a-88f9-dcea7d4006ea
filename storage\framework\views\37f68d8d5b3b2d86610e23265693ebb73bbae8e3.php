<?php $__env->startSection('title', 'แก้ไขกิจกรรม - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-warning"></i>แก้ไขกิจกรรม
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลกิจกรรม: <?php echo e($activity->title); ?></p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('admin.dashboard')); ?>">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('admin.activities.index')); ?>">
                                <i class="fas fa-images"></i> จัดการกิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขกิจกรรม
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขกิจกรรม: <?php echo e($activity->title); ?>

                            </h5>
                        </div>
                        
                        <form action="<?php echo e(route('admin.activities.update', $activity)); ?>" method="POST" enctype="multipart/form-data" id="activityForm">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <input type="hidden" name="remove_cover_image" id="removeCoverImageFlag" value="0">

                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="title" name="title" value="<?php echo e(old('title', $activity->title)); ?>" required>
                                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                      id="description" name="description" rows="4" required><?php echo e(old('description', $activity->description)); ?></textarea>
                                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                                    <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="category_id" name="category_id" required>
                                                        <option value="">เลือกหมวดหมู่</option>
                                                        <?php $__currentLoopData = \App\Models\ActivityCategory::active()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $activity->category_id) == $category->id ? 'selected' : ''); ?>>
                                                                <?php echo e($category->name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                                    <input type="date" class="form-control <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="activity_date" name="activity_date" value="<?php echo e(old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '')); ?>">
                                                    <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="location" class="form-label">สถานที่</label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="location" name="location" value="<?php echo e(old('location', $activity->location)); ?>" placeholder="สถานที่จัดกิจกรรม">
                                            <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                                                       <?php echo e(old('is_published', $activity->is_published) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="is_published">
                                                    เผยแพร่กิจกรรมนี้
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">รูปภาพหน้าปก</label>
                                            <input type="file" class="form-control <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="cover_image" name="cover_image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <!-- แสดงชื่อไฟล์ปัจจุบัน -->
                                        <div class="mb-2" id="currentFileName">
                                            <?php if($activity->cover_image): ?>
                                                <small class="text-primary">
                                                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong><?php echo e(basename($activity->cover_image)); ?></strong>
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">
                                                    <i class="fas fa-image"></i> ยังไม่มีรูปภาพ
                                                </small>
                                            <?php endif; ?>
                                        </div>

                                        <?php
                                            $hasImage = $activity->cover_image ? true : false;
                                            $imageUrl = $hasImage ? $activity->cover_image_url : '';
                                        ?>

                                        <?php if($hasImage): ?>
                                            <div id="imagePreview" style="display: block;">
                                                <img id="previewImg" src="<?php echo e($imageUrl); ?>"
                                                     class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                     alt="รูปภาพกิจกรรม"
                                                     onerror="this.src='<?php echo e(asset('images/no-image.svg')); ?>'; this.onerror=null;">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div id="imagePreview" style="display: none;">
                                                <img id="previewImg" src="<?php echo e(asset('images/no-image.svg')); ?>"
                                                     class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                     alt="ไม่มีรูปภาพ">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if(!$hasImage): ?>
                                            <div class="text-center text-muted py-4" id="noImageDisplay">
                                                <i class="fas fa-image fa-3x mb-3"></i>
                                                <p>ยังไม่มีรูปภาพหน้าปก</p>
                                                <small>เลือกไฟล์รูปภาพด้านบนเพื่อเพิ่มรูปภาพหน้าปก</small>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center text-muted py-4" id="noImageDisplay" style="display: none;">
                                                <i class="fas fa-image fa-3x mb-3"></i>
                                                <p>ยังไม่มีรูปภาพหน้าปก</p>
                                                <small>เลือกไฟล์รูปภาพด้านบนเพื่อเพิ่มรูปภาพหน้าปก</small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Gallery Images Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-header bg-success text-white">
                                                <h5 class="card-title mb-0">
                                                    <i class="fas fa-images"></i> รูปภาพแกลเลอรี่ (<?php echo e($activity->images->count()); ?> รูป)
                                                    <?php if(config('app.debug')): ?>
                                                        <small class="ms-2">Debug: Activity ID = <?php echo e($activity->id); ?></small>
                                                    <?php endif; ?>
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <!-- Upload New Images -->
                                                <div class="mb-4">
                                                    <label for="gallery_images" class="form-label">เพิ่มรูปภาพใหม่</label>
                                                    <input type="file" class="form-control <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                                    <small class="form-text text-muted">
                                                        สามารถเลือกหลายรูปพร้อมกัน | รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB ต่อรูป)
                                                    </small>
                                                    <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>

                                                <!-- Preview New Images -->
                                                <div id="newImagesPreview" class="mb-4" style="display: none;">
                                                    <h6 class="text-success"><i class="fas fa-plus"></i> รูปภาพใหม่ที่จะเพิ่ม</h6>
                                                    <div class="row" id="newImagesContainer"></div>
                                                    <div class="mt-2">
                                                        <button type="button" class="btn btn-sm btn-warning" id="clearNewImages">
                                                            <i class="fas fa-times"></i> ยกเลิกรูปภาพใหม่ทั้งหมด
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Existing Images -->
                                                <?php if($activity->images->count() > 0): ?>
                                                    <div class="mb-3">
                                                        <h6 class="text-primary"><i class="fas fa-images"></i> รูปภาพที่มีอยู่</h6>
                                                        <div class="row" id="existingImagesContainer">
                                                            <?php $__currentLoopData = $activity->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3" id="image-<?php echo e($image->id); ?>">
                                                                    <div class="card border-0 shadow-sm">
                                                                        <div class="position-relative">
                                                                            <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($image->image_path)); ?>"
                                                                                 class="card-img-top" style="height: 150px; object-fit: cover;"
                                                                                 alt="รูปภาพกิจกรรม"
                                                                                 onerror="this.src='<?php echo e(asset('images/no-image.svg')); ?>'; this.onerror=null;">
                                                                            <div class="position-absolute top-0 end-0 m-1">
                                                                                <button type="button" class="btn btn-sm btn-danger rounded-circle delete-image"
                                                                                        data-image-id="<?php echo e($image->id); ?>" data-image-path="<?php echo e($image->image_path); ?>">
                                                                                    <i class="fas fa-times"></i>
                                                                                </button>
                                                                            </div>
                                                                            <div class="position-absolute bottom-0 start-0 m-1">
                                                                                <span class="badge bg-dark"><?php echo e($loop->iteration); ?></span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="text-center text-muted py-4" id="noImagesMessage">
                                                        <i class="fas fa-images fa-3x mb-3"></i>
                                                        <p>ยังไม่มีรูปภาพในแกลเลอรี่</p>
                                                        <small>เลือกรูปภาพด้านบนเพื่อเพิ่มลงในแกลเลอรี่</small>
                                                    </div>
                                                <?php endif; ?>

                                                <!-- Hidden inputs for deleted images -->
                                                <div id="deletedImagesInputs"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('admin.activities.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-warning me-2">
                                            <i class="fas fa-save"></i> อัปเดตกิจกรรม
                                        </button>
                                        <button type="button" class="btn btn-danger" id="deleteActivityBtn">
                                            <i class="fas fa-trash"></i> ลบกิจกรรม
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus, .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255,193,7,.25);
}
.delete-image {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.position-relative:hover .delete-image {
    opacity: 1;
}
.position-relative .delete-image {
    opacity: 0.7;
    transition: opacity 0.2s;
}
.new-image-preview .card {
    border: 2px solid #198754 !important;
    animation: fadeIn 0.5s ease-in-out;
    transform-origin: center;
}
.new-image-preview .badge {
    animation: pulse 2s infinite;
}
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
.remove-new-image {
    transition: all 0.2s ease-in-out;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.remove-new-image:hover {
    transform: scale(1.1);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // ตรวจสอบว่ามีรูปจริงๆ หรือไม่
    const hasCoverImage = <?php echo e($activity->cover_image ? 'true' : 'false'); ?>;
    const originalImageUrl = "<?php echo e($activity->cover_image_url ?? ''); ?>";
    const originalImageName = "<?php echo e($activity->cover_image ? basename($activity->cover_image) : ''); ?>";

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    <?php if(session('success')): ?>
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '<?php echo e(session('success')); ?>',
            timer: 3000,
            showConfirmButton: false
        });
    <?php endif; ?>

    // Cover Image preview
    $('#cover_image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // ยกเลิกการลบรูปภาพ (ถ้ามี)
            $('#removeCoverImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();

                // อัปเดทชื่อไฟล์ที่แสดง
                $('#currentFileName').html(`
                    <small class="text-success">
                        <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${file.name}</strong>
                    </small>
                `);

                // เปลี่ยนปุ่มกลับเป็นลบรูปภาพ
                $('#undoRemove, #removeImage').html('<i class="fas fa-trash"></i> ลบรูปภาพ')
                    .removeClass('btn-warning').addClass('btn-danger').attr('id', 'removeImage');
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove cover image
    $(document).on('click', '#removeImage', function() {
        Swal.fire({
            title: 'ลบรูปภาพหน้าปก?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพหน้าปกนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ตั้งค่า flag ให้ลบรูปภาพ
                $('#removeCoverImageFlag').val('1');
                $('#cover_image').val('');

                // ซ่อนรูปภาพและแสดงข้อความไม่มีรูป
                $('#imagePreview').hide();
                $('#noImageDisplay').show();

                // ลบข้อความชื่อไฟล์ปัจจุบัน
                $('#currentFileName').html(`
                    <small class="text-danger">
                        <i class="fas fa-trash"></i> รูปภาพจะถูกลบเมื่อกดบันทึก
                    </small>
                `);

                // เปลี่ยนปุ่มเป็นยกเลิกการลบ
                $('#removeImage').html('<i class="fas fa-undo"></i> ยกเลิกการลบ')
                    .removeClass('btn-danger').addClass('btn-warning').attr('id', 'undoRemove');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ลบรูปภาพแล้ว!',
                    text: 'รูปภาพจะถูกลบเมื่อคุณกดบันทึก',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Undo remove cover image
    $(document).on('click', '#undoRemove', function() {
        Swal.fire({
            title: 'ยกเลิกการลบ?',
            text: 'คุณต้องการยกเลิกการลบรูปภาพหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-undo"></i> ยกเลิกการลบ',
            cancelButtonText: '<i class="fas fa-times"></i> ไม่ต้อง',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ยกเลิกการลบ
                $('#removeCoverImageFlag').val('0');

                if (hasCoverImage) {
                    $('#imagePreview').show();
                    $('#noImageDisplay').hide();
                    $('#previewImg').attr('src', originalImageUrl);

                    // กลับไปแสดงชื่อไฟล์เดิม
                    if (originalImageName) {
                        $('#currentFileName').html(`
                            <small class="text-primary">
                                <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                            </small>
                        `);
                    }
                }

                // เปลี่ยนปุ่มกลับ
                $('#undoRemove').html('<i class="fas fa-trash"></i> ลบรูปภาพ').removeClass('btn-warning').addClass('btn-danger');
                $('#undoRemove').attr('id', 'removeImage');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ยกเลิกแล้ว!',
                    text: 'รูปภาพจะไม่ถูกลบ',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Gallery Images Preview
    let deletedImages = [];
    let newImageFiles = [];

    $('#gallery_images').on('change', function() {
        let files = this.files;
        newImageFiles = Array.from(files); // Store files for form submission

        if (files.length > 0) {
            $('#newImagesPreview').show();
            $('#newImagesContainer').empty();

            for (let i = 0; i < files.length; i++) {
                let file = files[i];
                let reader = new FileReader();

                reader.onload = function(e) {
                    let imageCard = `
                        <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3 new-image-preview" data-file-index="${i}">
                            <div class="card border-success shadow-sm">
                                <div class="position-relative">
                                    <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="รูปภาพใหม่">
                                    <div class="position-absolute top-0 end-0 m-1">
                                        <button type="button" class="btn btn-sm btn-danger rounded-circle remove-new-image" data-file-index="${i}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="position-absolute bottom-0 start-0 m-1">
                                        <span class="badge bg-success">ใหม่</span>
                                    </div>
                                </div>
                                <div class="card-body p-2">
                                    <small class="text-success">ไฟล์: ${file.name}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#newImagesContainer').append(imageCard);
                };

                reader.readAsDataURL(file);
            }
        } else {
            $('#newImagesPreview').hide();
        }
    });

    // Remove individual new image
    $(document).on('click', '.remove-new-image', function() {
        let fileIndex = $(this).data('file-index');
        let removedElement = $(this).closest('.new-image-preview');

        // Remove from newImageFiles array
        newImageFiles.splice(fileIndex, 1);

        // Update file input
        let dt = new DataTransfer();
        newImageFiles.forEach(file => dt.items.add(file));
        document.getElementById('gallery_images').files = dt.files;

        // Remove the element with animation
        removedElement.fadeOut(300, function() {
            $(this).remove();

            // Update file indices for remaining new images
            $('.new-image-preview').each(function(index) {
                $(this).attr('data-file-index', index);
                $(this).find('.remove-new-image').attr('data-file-index', index);
            });

            // Hide preview section if no new images
            if ($('.new-image-preview').length === 0) {
                $('#newImagesPreview').hide();
            }
        });
    });

    // Clear New Images
    $('#clearNewImages').on('click', function() {
        Swal.fire({
            title: 'ยกเลิกรูปภาพใหม่?',
            text: 'คุณต้องการยกเลิกรูปภาพใหม่ทั้งหมดหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            cancelButtonText: '<i class="fas fa-arrow-left"></i> กลับ'
        }).then((result) => {
            if (result.isConfirmed) {
                $('#gallery_images').val('');
                $('#newImagesPreview').hide();
                $('#newImagesContainer').empty();
                newImageFiles = [];
            }
        });
    });

    // Delete Existing Image
    $(document).on('click', '.delete-image', function() {
        let imageId = $(this).data('image-id');
        let imagePath = $(this).data('image-path');

        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Add to deleted images array
                deletedImages.push(imageId);

                // Add hidden input for deleted image
                $('#deletedImagesInputs').append(`
                    <input type="hidden" name="deleted_images[]" value="${imageId}">
                `);

                // Remove from DOM
                $(`#image-${imageId}`).fadeOut(300, function() {
                    $(this).remove();

                    // Check if there are no more images
                    if ($('#existingImagesContainer').children().length === 0) {
                        $('#existingImagesContainer').html(`
                            <div class="text-center text-muted py-4 w-100">
                                <i class="fas fa-images fa-3x mb-3"></i>
                                <p>ไม่มีรูปภาพเหลืออยู่</p>
                            </div>
                        `);
                    }
                });

                Swal.fire({
                    title: 'ลบรูปภาพแล้ว!',
                    text: 'รูปภาพจะถูกลบเมื่อคุณกดบันทึก',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Form validation
    $('#activityForm').on('submit', function(e) {
        let isValid = true;

        // Clear previous errors
        $('.form-control, .form-select').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Validate title
        if (!$('#title').val().trim()) {
            $('#title').addClass('is-invalid');
            $('#title').after('<div class="invalid-feedback">กรุณากรอกชื่อกิจกรรม</div>');
            isValid = false;
        }

        // Validate description
        if (!$('#description').val().trim()) {
            $('#description').addClass('is-invalid');
            $('#description').after('<div class="invalid-feedback">กรุณากรอกรายละเอียดกิจกรรม</div>');
            isValid = false;
        }

        // Validate category
        if (!$('#category_id').val()) {
            $('#category_id').addClass('is-invalid');
            $('#category_id').after('<div class="invalid-feedback">กรุณาเลือกหมวดหมู่</div>');
            isValid = false;
        }

        // Debug: Log form data before submit
        if (isValid) {
            console.log('Form data being submitted:');
            console.log('Cover image files:', $('#cover_image')[0].files.length);
            console.log('Gallery image files:', $('#gallery_images')[0].files.length);
            console.log('Remove cover image flag:', $('#removeCoverImageFlag').val());
            console.log('Deleted images:', $('input[name="deleted_images[]"]').length);
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    // Auto-resize textarea
    $('#description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Delete activity
    $('#deleteActivityBtn').on('click', function(e) {
        e.preventDefault();

        Swal.fire({
            title: 'ลบกิจกรรม?',
            html: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม<br><strong>"<?php echo e($activity->title); ?>"</strong>?<br><small class="text-danger">รูปภาพทั้งหมดจะถูกลบด้วย</small><br><small class="text-muted">การกระทำนี้ไม่สามารถยกเลิกได้</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: 'กำลังลบกิจกรรมและรูปภาพทั้งหมด',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // สร้าง form และ submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': '<?php echo e(route('admin.activities.destroy', $activity)); ?>'
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': $('meta[name="csrf-token"]').attr('content')
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activities/edit.blade.php ENDPATH**/ ?>